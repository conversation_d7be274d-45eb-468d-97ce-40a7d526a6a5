import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons';
import { Form, Modal, App, Drawer } from 'antd';
import React, { useCallback, useState, useRef, useEffect } from 'react';

// 导入重构后的模块
import type { AlertSend, AlertSendSearchParams } from '../types';
import { DEFAULT_PAGINATION } from '../constants';
import { TaskService } from '../services';
import { useAlertSendData, useDrawer } from '../hooks';
import { useAlertSendTable } from '../hooks/useAlertSendTable';
import { useSelection } from '../hooks/useSelection';
import { tableStyles } from '../styles';

// 导入拆分的组件
import { createAlertSendTableColumns } from './AlertSendTableColumns';
import { AlertSendQuickSearchForm } from './forms/AlertSendSearchForm';
import { AlertSendActionButtons } from './AlertSendActionButtons';
import { AlertSendTableComponent } from './AlertSendTableComponent';
import AlertSendBasicForm from './forms/AlertSendBasicForm';

interface AlertSendTableProps {
  contentHeight?: number;
}

/**
 * 告警发送管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AlertSendTable: React.FC<AlertSendTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const {
    data,
    loading,
    total,
    pagination,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  } = useAlertSendData({ autoLoad: true });

  const { allSelectedRows, rowSelection, clearSelection, getSelectedCount } = useSelection(data, {
    crossPage: true,
    onSelectionChange: (selectedKeys: React.Key[], selectedRows: AlertSend[]) => {
      console.log('选择变化:', { selectedKeys, selectedRows });
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } =
    useAlertSendTable({ contentHeight });

  // 抽屉状态管理
  const {
    open: editDrawerOpen,
    show: showEditDrawer,
    hide: hideEditDrawer,
    config: editDrawerConfig,
  } = useDrawer();

  // 表单和数据状态
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [currentRecord, setCurrentRecord] = useState<AlertSend | null>(null);

  // 数据加载引用
  const loadDataRef = useRef(loadData);
  loadDataRef.current = loadData;

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: AlertSendSearchParams) => {
      console.log('搜索参数:', values);
      updateSearchParams(values);
      updatePagination(1, pagination.page_size);
      loadDataRef.current({
        ...values,
        current: 1,
        page_size: pagination.page_size,
      });
    },
    [updateSearchParams, updatePagination, pagination.page_size]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    searchForm.resetFields();
    resetSortAndFilter();
    resetData();
    updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);
    loadDataRef.current({
      current: DEFAULT_PAGINATION.current,
      page_size: DEFAULT_PAGINATION.page_size,
    });
  }, [searchForm, resetSortAndFilter, resetData, updatePagination]);

  // 编辑处理
  const handleEdit = useCallback((record: AlertSend) => {
    console.log('编辑告警发送:', record);
    setCurrentRecord(record);
    editForm.setFieldsValue(record);
    showEditDrawer({
      title: '编辑告警发送',
      width: '60%',
    });
  }, [editForm, showEditDrawer]);

  // 删除处理
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        console.log('删除告警发送:', id);
        await TaskService.deleteAlertSend(id);
        message.success('删除成功');
        await refreshData();
        clearSelection();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
    [message, refreshData, clearSelection]
  );

  // 批量删除处理
  const handleBatchDelete = useCallback(() => {
    if (allSelectedRows.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${allSelectedRows.length} 条告警发送配置吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const deletePromises = allSelectedRows.map(row => TaskService.deleteAlertSend(row.id));
          await Promise.all(deletePromises);
          message.success(`成功删除 ${allSelectedRows.length} 条记录`);
          await refreshData();
          clearSelection();
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  }, [allSelectedRows, message, refreshData, clearSelection]);

  // 表单提交处理
  const handleFormSubmit = useCallback(
    async (values: any) => {
      try {
        console.log('提交告警发送表单:', values);
        
        if (currentRecord) {
          // 编辑模式
          await TaskService.updateAlertSend(currentRecord.id, values);
          message.success('更新成功');
        } else {
          // 新增模式
          await TaskService.addAlertSend(values);
          message.success('新增成功');
        }

        hideEditDrawer();
        editForm.resetFields();
        setCurrentRecord(null);
        await refreshData();
      } catch (error) {
        console.error('提交失败:', error);
        message.error('提交失败');
      }
    },
    [currentRecord, message, hideEditDrawer, editForm, refreshData]
  );

  // 取消编辑
  const handleCancel = useCallback(() => {
    hideEditDrawer();
    editForm.resetFields();
    setCurrentRecord(null);
  }, [hideEditDrawer, editForm]);

  // 表格列定义
  const columns = createAlertSendTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <AlertSendQuickSearchForm
          form={searchForm}
          onSubmit={handleSearchFormSubmit}
          onReset={handleReset}
        />

        {/* 操作按钮区域 */}
        <AlertSendActionButtons
          selectedCount={getSelectedCount()}
          onAddAlertSend={() => {
            setCurrentRecord(null);
            editForm.resetFields();
            showEditDrawer({
              title: '新增告警发送',
              width: '60%',
            });
          }}
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <AlertSendTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page: number, pageSize: number) => {
            updatePagination(page, pageSize);
            loadDataRef.current({
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={editDrawerConfig.title}
        width={editDrawerConfig.width}
        open={editDrawerOpen}
        onClose={handleCancel}
        destroyOnClose
      >
        <AlertSendBasicForm
          form={editForm}
          initialData={currentRecord}
          onSubmit={handleFormSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </Drawer>
    </div>
  );
};

export default AlertSendTable;
