import React from 'react';
import { Form, Input, Select, Button, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { AlertSendSearchParams } from '../../types';
import { tableStyles } from '../../styles';

const { Option } = Select;

interface AlertSendQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: AlertSendSearchParams) => void;
  onReset: () => void;
}

/**
 * 告警发送快速搜索表单组件
 * 提供告警发送的快速搜索功能
 */
export const AlertSendQuickSearchForm: React.FC<AlertSendQuickSearchFormProps> = ({
  form,
  onSubmit,
  onReset,
}) => {
  const handleSubmit = (values: any) => {
    // 过滤空值
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);

    console.log('告警发送搜索参数:', filteredValues);
    onSubmit(filteredValues);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <div className={tableStyles.searchFormContainer}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleSubmit}
        className={tableStyles.searchForm}
      >
        <Row gutter={16} className="w-full">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              name="name"
              label="发送名称"
              className={tableStyles.searchFormItem}
            >
              <Input
                placeholder="请输入发送名称"
                allowClear
                className={tableStyles.searchInput}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              name="type"
              label="接收类型"
              className={tableStyles.searchFormItem}
            >
              <Select
                placeholder="请选择接收类型"
                allowClear
                className={tableStyles.searchSelect}
              >
                <Option value="kafka">Kafka</Option>
                <Option value="prometheus">Prometheus</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={8} lg={12}>
            <Form.Item className={tableStyles.searchButtonGroup}>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                className={tableStyles.searchButton}
              >
                搜索
              </Button>
              <Button
                onClick={handleReset}
                icon={<ReloadOutlined />}
                className={tableStyles.resetButton}
              >
                重置
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
