import React from 'react';
import { Form, Input, But<PERSON>, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { OtherInfoSearchParams } from '../../types';
import { tableStyles } from '../../styles';

interface OtherInfoQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: OtherInfoSearchParams) => void;
  onReset: () => void;
}

/**
 * 其他信息快速搜索表单组件
 * 提供其他信息的快速搜索功能
 */
export const OtherInfoQuickSearchForm: React.FC<OtherInfoQuickSearchFormProps> = ({
  form,
  onSubmit,
  onReset,
}) => {
  const handleSubmit = (values: any) => {
    // 过滤空值
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);

    console.log('其他信息搜索参数:', filteredValues);
    onSubmit(filteredValues);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <div className={tableStyles.searchFormContainer}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleSubmit}
        className={tableStyles.searchForm}
      >
        <Row gutter={16} className="w-full">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              name="name"
              label="信息名称"
              className={tableStyles.searchFormItem}
            >
              <Input
                placeholder="请输入信息名称"
                allowClear
                className={tableStyles.searchInput}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              name="business"
              label="业务系统"
              className={tableStyles.searchFormItem}
            >
              <Input
                placeholder="请输入业务系统"
                allowClear
                className={tableStyles.searchInput}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              name="hostname"
              label="主机名称"
              className={tableStyles.searchFormItem}
            >
              <Input
                placeholder="请输入主机名称"
                allowClear
                className={tableStyles.searchInput}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={8} lg={6}>
            <Form.Item className={tableStyles.searchButtonGroup}>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                className={tableStyles.searchButton}
              >
                搜索
              </Button>
              <Button
                onClick={handleReset}
                icon={<ReloadOutlined />}
                className={tableStyles.resetButton}
              >
                重置
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
